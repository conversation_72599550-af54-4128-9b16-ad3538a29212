/* Welcome Screen Styles */
.welcome-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    background: var(--color-background);
    color: var(--color-text);
    height: 100%;
    overflow-y: auto;
}

.welcome-logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
}

.welcome-logo {
    width: 120px;
    height: 120px;
    filter: brightness(1);
}

/* Dark mode adjustments for logo */
.dark-mode .welcome-logo {
    filter: brightness(1.2);
}

.welcome-title {
    font-size: 2rem;
    font-weight: 300;
    margin-bottom: 0.8rem;
    color: var(--color-text);
}

.welcome-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
    color: var(--color-secondary);
    max-width: 600px;
    line-height: 1.5;
}

.welcome-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
    max-width: 520px;
    width: 100%;
    margin: 1.5rem 0;
}

.welcome-action-card {
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 120px;
    justify-content: center;
}

.welcome-action-card:hover {
    border-color: var(--color-accent);
    background: var(--color-message-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .welcome-action-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.welcome-action-icon {
    font-size: 2rem;
    margin-bottom: 0.8rem;
    color: var(--color-accent);
}

.welcome-action-title {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.4rem;
    color: var(--color-text);
}

.welcome-action-description {
    font-size: 0.8rem;
    color: var(--color-secondary);
    line-height: 1.3;
}

.welcome-footer {
    margin-top: 1.5rem;
    opacity: 0.7;
    font-size: 0.8rem;
}

/* Light mode adjustments */
.light-mode .welcome-title {
    color: #2d2d2d;
}

.light-mode .welcome-subtitle {
    color: #666;
}

.light-mode .welcome-action-title {
    color: #2d2d2d;
}

.light-mode .welcome-action-description {
    color: #666;
}

.light-mode .welcome-footer {
    color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
    .welcome-container {
        padding: 1rem;
    }
    
    .welcome-title {
        font-size: 2rem;
    }
    
    .welcome-subtitle {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .welcome-actions {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .welcome-action-card {
        padding: 1.5rem;
        min-height: 120px;
    }
    
    .welcome-action-icon {
        font-size: 2rem;
    }
    
    .welcome-action-title {
        font-size: 1.1rem;
    }
}